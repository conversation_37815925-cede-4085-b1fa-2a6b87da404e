from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2Authorization<PERSON>odeBearer
from jose import jwt, JWTError
import httpx
from dotenv import load_dotenv
import os

load_dotenv()

OKTA_ISSUER = os.getenv("OKTA_ISSUER")
OKTA_CLIENT_ID = os.getenv("OKTA_CLIENT_ID")

oauth2_scheme = OAuth2AuthorizationCodeBearer(
    authorizationUrl=f"{OKTA_ISSUER}/authorize",
    tokenUrl=f"{OKTA_ISSUER}/token",
)

async def get_current_user(token: str = Depends(oauth2_scheme)):
    try:
        async with httpx.AsyncClient() as client:
            jwks = await client.get(f"{OKTA_ISSUER}/keys")
            jwks = jwks.json()
            payload = jwt.decode(token, jwks, audience=OKTA_CLIENT_ID, issuer=OKTA_ISSUER)
            return payload
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token",
            headers={"WWW-Authenticate": "Bearer"},
        )
