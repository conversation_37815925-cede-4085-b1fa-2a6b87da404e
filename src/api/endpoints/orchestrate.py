from fastapi import APIRouter, Depends
from models.a2a import WorkflowTask
from services.orchestrator import orchestrate_workflow
from api.dependencies.auth import get_current_user

router = APIRouter()

@router.post("/orchestrate")
async def run_workflow(task: WorkflowTask, user: dict = Depends(get_current_user)):
    results = await orchestrate_workflow(task)
    return {"workflow_results": results}
