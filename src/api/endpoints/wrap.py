from fastapi import APIRouter, Depends
from models.a2a import MultiAPIRequest, AgentCard
from services.wrapper import wrap_api
from api.dependencies.auth import get_current_user

router = APIRouter()

@router.post("/wrap-multi", response_model=list[AgentCard])
async def wrap_multiple_apis(request: MultiAPIRequest, user: dict = Depends(get_current_user)):
    wrapped_apis = []
    for api in request.apis:
        agent_card = await wrap_api(api)
        wrapped_apis.append(agent_card)
    return wrapped_apis
