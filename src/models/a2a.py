from pydantic import BaseModel
from typing import List, Dict, Any

class AgentCard(BaseModel):
    id: str
    name: str
    description: str
    auth: Dict[str, str] = {"type": "OAuth2", "idp": "https://your-okta-domain.okta.com"}

class SendMessageRequest(BaseModel):
    agent_id: str
    payload: Dict[str, Any]

class LegacyAPIInput(BaseModel):
    endpoint: str
    data: Dict[str, Any]

class MultiAPIRequest(BaseModel):
    apis: List[LegacyAPIInput]

class WorkflowTask(BaseModel):
    task: str
    agents: List[str]

class CatalogEntry(BaseModel):
    agent_card: AgentCard
    compliance: List[str]
