# A2A Compliance SaaS Backend

Backend for the A2A Compliance SaaS platform, built with FastAPI, Python, and PostgreSQL. Supports wrapping legacy APIs into A2A-compliant endpoints, orchestrating workflows, and Okta OAuth 2.0 authentication.

## Setup

1. Activate virtual environment:
   ```bash
   source venv/bin/activate
   ```

2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Update `.env` with Okta and PostgreSQL credentials.

4. Run the server:
   ```bash
   uvicorn src.main:app --reload --port 8000
   ```

5. Test endpoints at .

## Deployment

Deploy to GCP Cloud Run:
```bash
docker build -t gcr.io/your-gcp-project/a2a-saas-backend .
gcloud auth configure-docker
docker push gcr.io/your-gcp-project/a2a-saas-backend
gcloud run deploy a2a-saas-backend --image gcr.io/your-gcp-project/a2a-saas-backend --region us-central1 --allow-unauthenticated
```
